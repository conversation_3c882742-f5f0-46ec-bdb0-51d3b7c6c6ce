# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type FaceInformation {
  faceId: String
}

type User {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """user fullname"""
  fullname: String!

  """user phone number"""
  phone: String!

  """user active status"""
  userStatus: UserStatus!

  """user role"""
  role: UserRoles!
  faceInformation: FaceInformation
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum UserRoles {
  ADMIN
  OPERATIONS_ADMIN
  OPERATIONS_MANAGER
  HR_ADMIN
  LOCAL_GUARD
  NEPAL_GUARD
  BUFFER_GUARD
}

"""The health of the server"""
type Health {
  status: Boolean!
}

type AuthOutput {
  """Access token"""
  access_token: String!
}

type PresignedFields {
  key: String!
  bucket: String!
  acl: String!
  algorithm: String!
  credential: String!
  date: String!
  Policy: String!
  signature: String!
}

type SignedUploadUrl {
  url: String!
  fields: PresignedFields!
}

type Checkpoint {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  location: Location!
  locationCoordinates: [Float!]
  isDeleted: Boolean!
  deletedAt: DateTime
}

type Location {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  description: String
  address: String
  emergencyContact: String
  checkpoints: [Checkpoint!]
}

type Shift {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  location: Location
  startDateTime: DateTime!
  endDateTime: DateTime!
  overTime: DateTime
  users: [User!]
  isRecurring: Boolean
  recurringId: String
}

type Storage {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  type: StorageItemType!
  parent: Storage
  size: Float!
  path: String
  source: StorageItemSource!
  metadata: String
}

enum StorageItemType {
  FILE
  FOLDER
}

enum StorageItemSource {
  USER
  SYSTEM
}

type AttributeType {
  attibuteName: String!
  attributeValues: [String!]!
}

type InventoryItem {
  item: String!
  quantity: Int!
  sku: String!
  costPrice: Float!
  sellingPrice: Float!
}

type Inventory {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """Inventory item name"""
  item: String!
  description: String
  attributes: [AttributeType!]
  items: [InventoryItem!]
  type: InventoryType!
}

enum InventoryType {
  LOCATION
  GUARD
}

"""User/Employee allowances"""
type Allowance {
  """allowance created by"""
  user: User!

  """location allowance"""
  location: Location

  """Type of allowance"""
  type: String!

  """Amount of allowance"""
  amount: Float!

  """Status of allowance"""
  status: AllowanceStatus

  """Description of allowance"""
  description: String

  """allowance receipt"""
  receipt: String
}

enum AllowanceStatus {
  PENDING
  APPROVED
  REJECTED
}

type Anouncement {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  users: [User!]
  userRoles: [UserRoles!]
  title: String!
  description: String!
  date: DateTime!
  document: String
}

type UserDocument {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  user: User!
  documentName: String!
  url: String!
}

type Holiday {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  description: String!
  date: DateTime!
}

type SelectedAttribute {
  attributeName: String!
  value: String!
}

type RequestedItem {
  item: String!
  sku: String!
  quantity: Int!
  selectedAttributes: [SelectedAttribute!]!
  costPrice: Float!
  sellingPrice: Float!
}

type InventoryRequest {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  inventory: Inventory!
  items: [RequestedItem!]!
  requestedBy: User!
  acceptedBy: User
  status: RequestStatus!
  acceptedAt: DateTime
  inventoryType: InventoryType!
}

enum RequestStatus {
  PENDING
  ACCEPTED
  REJECTED
}

type Leave {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  user: User
  reason: String!
  leaveType: LeaveType!
  startDateTime: DateTime!
  endDateTime: DateTime!
  leaveStatus: LeaveStatus
  rejectedReason: String
  approvedBy: User
}

enum LeaveType {
  FULLDAY
  HALFDAY
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
}

type PaymentCorrection {
  amount: Float!
  user: User!
  reason: String!
  date: DateTime!
  correctedBy: User!
}

type Task {
  location: Location!
  shift: Shift!
  users: [User!]!
  title: String!
  description: String!
  taskStatus: TaskStatus!
  isRecurring: Boolean!
  shiftRecurringId: String
  date: DateTime!
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
}

type Contact {
  countryCode: String!
  phone: String!
}

type EmergencyContact {
  name: String!
  relation: String!
  contact: Contact!
}

type UserProfile {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  user: String!
  ic: String
  ID: String
  passport: String
  passportExpiresAt: DateTime
  permitNumber: String
  permitExpiresAt: DateTime
  gender: String
  dob: DateTime
  placeOfBirth: String
  currentAddress: String
  joinedAt: DateTime
  maritalStatus: String
  bankAccNumber: String
  bankName: String
  emergencyContact: [EmergencyContact!]
}

"""User attendance"""
type Attendance {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  user: User
  date: DateTime!
  timeSpentInMinutes: Int!
  overTimeSpentInMinutes: Int!
  startTime: DateTime!
  endTime: DateTime
  overTime: DateTime
  shift: Shift
  location: Location
}

type PaymentConfigOption {
  paymentType: PaymentType!
  fullTimeAmount: Float!
  overTimeAmount: Float!
}

enum PaymentType {
  HOURLY
  DAILY
  MONTHLY
}

type PaymentConfig {
  location: Location!
  roles: [UserRoles!]!
  weekDay: PaymentConfigOption!
  weekOff: PaymentConfigOption!
  holiday: PaymentConfigOption!
}

type Claims {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """Claim status"""
  status: ClaimStatus

  """Type of the claim"""
  claimType: ClaimType!

  """claim data in calims"""
  claimData: ClaimUnion!

  """Claim processed by"""
  processedBy: User
  rejectedReason: String
}

enum ClaimStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ClaimType {
  ALLOWANCE
  TRAVEL
  EXPENSE
  SITE
}

union ClaimUnion = AllowanceClaim | TravelClaim | ExpenseClaim | SiteClaim

type AllowanceClaim {
  """Claim created by"""
  user: User!

  """Claim amount"""
  amount: Float

  """Purpose of the claim"""
  purpose: String
  from: DateTime!
  to: DateTime!
  workingHours: Int!
  receipts: [String!]
}

type TravelClaim {
  """Claim created by"""
  user: User!

  """Claim amount"""
  amount: Float

  """Purpose of the claim"""
  purpose: String
  from: DateTime!
  to: DateTime!
  client: String!
  toll: String!
  distance: Int!
  receipts: [String!]!
}

type ExpenseClaim {
  """Claim created by"""
  user: User!

  """Claim amount"""
  amount: Float

  """Purpose of the claim"""
  purpose: String

  """List of items"""
  items: [String!]!
  date: DateTime!
  receipts: [String!]!
}

type SiteClaim {
  """Claim created by"""
  user: User!

  """Claim amount"""
  amount: Float

  """Purpose of the claim"""
  purpose: String
  site: Location!
  items: [String!]!
  receipts: [String!]!
}

type CheckpointData {
  name: String!
  locationCoordinates: [Float!]
}

type CheckpointAttendance {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  checkpoint: Checkpoint!
  checkpointData: CheckpointData!
  location: Location!
  guard: User!
  scannedAt: DateTime!
  scannedLocation: [Float!]
}

type Evidence {
  type: EvidenceType!
  url: String!
}

enum EvidenceType {
  IMAGE
  VIDEO
  DOCUMENT
}

type Incident {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  location: Location!
  reportedAt: DateTime!
  description: String!
  priorityLevel: PriorityLevel!
  evidence: [Evidence!]!
  reportedBy: User!
}

enum PriorityLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

type TimeSeriesData {
  date: String!
  count: Int!
}

type GuardActivityStats {
  totalAttendance: Int!
  averageTimeSpent: Float!
  totalCheckpoints: Int!
  totalIncidents: Int!
}

type LeaveStats {
  pending: Int!
  approved: Int!
  rejected: Int!
}

type InventoryStats {
  type: String!
  totalItems: Int!
  requestsPending: Int!
}

type Query {
  locations: [Location!]!
  location(id: String!): Location!
  storageItems(parentId: String): [Storage!]!
  systemFiles: [Storage!]!
  storageFolders(parentId: String): [Storage!]!
  users(usersInput: UsersInput): [User!]!
  user(id: String!): User!
  shifts(shiftsInput: ShiftsInput!): [Shift!]!
  shift(shiftId: String!): Shift!
  getUserShifts(shiftsInput: ShiftsInput!): [Shift!]!
  getShiftsByLocation(locationId: String!): Shift!
  health: Health!

  """Logged in  user"""
  me: User!
  allowances: [Allowance!]!
  allowance(id: Int!): Allowance!
  anouncements: [Anouncement!]!
  anouncement(id: String!): Anouncement!
  userDocuments(filter: UserDocumentFilterInput): [UserDocument!]!
  userDocument(id: String!): UserDocument!
  holidays: [Holiday!]!
  holiday(id: String!): Holiday!
  inventory(inventoryInput: InventoryInput): [Inventory!]!
  inventoryRequests(filter: FindInventoryRequestsInput): [InventoryRequest!]!
  inventoryRequest(id: String!): InventoryRequest!
  leaves(filter: FindLeavesInput): [Leave!]!
  leave(id: String!): Leave!
  paymentCorrections: [PaymentCorrection!]!
  paymentCorrection(id: Int!): PaymentCorrection!
  tasks: [Task!]!
  task(id: Int!): Task!
  userProfiles: [UserProfile!]!
  userProfile(id: String!): UserProfile!
  getAttendanceById(id: String!): Attendance!
  allAttendances: [Attendance!]!
  attendance(attendanceInput: AttendanceInput!): [Attendance!]!
  paymentConfig(id: Int!): PaymentConfig!
  claims(filter: FindClaimsInput): [Claims!]!
  claim(id: String!): Claims!
  checkpoints(filter: FindCheckpointsInput): [Checkpoint!]!
  checkpoint(id: String!): Checkpoint!
  checkpointAttendances: [CheckpointAttendance!]!
  checkpointAttendance(id: String!): CheckpointAttendance!
  getCheckpointAttendances(checkpointId: String!): [CheckpointAttendance!]!
  getGuardAttendances(guardId: String!): [CheckpointAttendance!]!
  getLocationAttendances(locationId: String!): [CheckpointAttendance!]!
  incidents: [Incident!]!
  incident(id: String!): Incident!
  guardAttendanceTrends(filter: AnalyticsFilterInput): [TimeSeriesData!]!
  guardActivityStats(filter: AnalyticsFilterInput): GuardActivityStats!
  leaveStats(filter: AnalyticsFilterInput): LeaveStats!
  inventoryStats: [InventoryStats!]!
}

input UsersInput {
  roles: [UserRoles!]
}

input ShiftsInput {
  locationId: ID
  startDateTime: DateTime!
  endDateTime: DateTime!
  userId: ID
}

input UserDocumentFilterInput {
  user: String
}

input InventoryInput {
  inventoryType: InventoryType!
}

input FindInventoryRequestsInput {
  inventoryType: InventoryType
  status: RequestStatus
  requestedBy: String
}

input FindLeavesInput {
  user: String
  leaveStatus: String
  leaveType: String
}

input AttendanceInput {
  shiftId: String!
}

input FindClaimsInput {
  user: String
  claimType: ClaimType
  status: String
}

input FindCheckpointsInput {
  location: String
}

input AnalyticsFilterInput {
  dateRange: DateRangeInput
  userRole: UserRoles
  locationId: String
}

input DateRangeInput {
  startDate: DateTime!
  endDate: DateTime!
}

type Mutation {
  createLocation(createLocationInput: CreateLocationInput!): Location!
  updateLocation(id: ID!, updateLocationInput: UpdateLocationInput!): Location!
  removeLocation(id: ID!): Location!
  createFolder(createFolderInput: CreateFolderDto!): Storage!
  updateUser(updateUserInput: UpdateUserInput!): User!
  indexFace(indexFaceInput: IndexFaceInput!): User!
  clearFace(clearFaceInput: ClearFaceInput!): Boolean!
  createSignedUploadUrl(input: SignedUploadUrlInput!): SignedUploadUrl!
  createShift(createShiftInput: CreateShiftInput!): Boolean
  updateShift(shiftId: String!, updateShiftInput: UpdateShiftInput!): Shift!
  removeShift(shiftId: String!): Boolean
  removeRecurringShifts(shiftId: String!, recurringId: String!): Boolean
  signIn(input: SignInInput!): AuthOutput!
  signUp(input: CreateUserInput!): AuthOutput!
  createAllowance(createAllowanceInput: CreateAllowanceInput!): Allowance!
  updateAllowance(updateAllowanceInput: UpdateAllowanceInput!): Allowance!
  removeAllowance(id: Int!): Allowance!
  createAnouncement(createAnouncementInput: CreateAnouncementInput!): Anouncement!
  updateAnouncement(id: String!, updateAnouncementInput: UpdateAnouncementInput!): Anouncement!
  removeAnouncement(id: String!): Anouncement!
  createUserDocument(createUserDocumentInput: CreateUserDocumentInput!): UserDocument!
  updateUserDocument(id: String!, updateUserDocumentInput: UpdateUserDocumentInput!): UserDocument!
  removeUserDocument(id: String!): UserDocument!
  createHoliday(createHolidayInput: CreateHolidayInput!): Holiday!
  updateHoliday(updateHolidayInput: UpdateHolidayInput!, id: String!): Holiday!
  removeHoliday(id: String!): Holiday!
  createInventory(createInventoryInput: CreateInventoryInput!): Inventory!
  updateInventory(id: String!, updateInventoryInput: CreateInventoryInput!): Inventory!
  createInventoryRequest(input: CreateInventoryRequestInput!): InventoryRequest!
  updateInventoryRequest(id: String!, input: UpdateInventoryRequestInput!): InventoryRequest!
  createLeave(createLeaveInput: CreateLeaveInput!): Leave!
  updateLeave(id: String!, updateLeaveInput: UpdateLeaveInput!): Leave!
  createPaymentCorrection(createPaymentCorrectionInput: CreatePaymentCorrectionInput!): PaymentCorrection!
  updatePaymentCorrection(updatePaymentCorrectionInput: UpdatePaymentCorrectionInput!): PaymentCorrection!
  removePaymentCorrection(id: Int!): PaymentCorrection!
  createTask(createTaskInput: CreateTaskInput!): Task!
  updateTask(updateTaskInput: UpdateTaskInput!): Task!
  removeTask(id: Int!): Task!
  createUserProfile(createUserProfileInput: CreateUserProfileInput!): UserProfile!
  updateUserProfile(id: String!, updateUserProfileInput: UpdateUserProfileInput!): UserProfile!
  removeUserProfile(id: String!): UserProfile!
  clockIn(clockInInput: ClockInInput!): Attendance!
  clockOut(clockOutInput: ClockOutInput!): Attendance!
  createAttendance(createAttendanceInput: CreateAttendanceInput!): Attendance!
  updateAttendance(updateAttendanceInput: UpdateAttendanceInput!): Attendance!
  createPaymentConfig(createPaymentConfigInput: CreatePaymentConfigInput!): PaymentConfig!
  updatePaymentConfig(updatePaymentConfigInput: UpdatePaymentConfigInput!): PaymentConfig!
  removePaymentConfig(id: Int!): PaymentConfig!
  createClaim(input: ClaimInput!): Claims!
  updateClaim(id: String!, updateClaimInput: UpdateClaimInput!): Claims!
  createCheckpoint(createCheckpointInput: CreateCheckpointInput!): Checkpoint!
  updateCheckpoint(id: String!, updateCheckpointInput: UpdateCheckpointInput!): Checkpoint!
  removeCheckpoint(id: String!): Checkpoint!
  createCheckpointAttendance(createCheckpointAttendanceInput: CreateCheckpointAttendanceInput!): CheckpointAttendance!
  createIncident(createIncidentInput: CreateIncidentInput!): Incident!
  updateIncident(id: String!, updateIncidentInput: UpdateIncidentInput!): Incident!
}

input CreateLocationInput {
  name: String!
  description: String
  address: String
  emergencyContact: String
}

input UpdateLocationInput {
  name: String
  description: String
  address: String
  emergencyContact: String
}

input CreateFolderDto {
  folder: String!
  parentFolderId: String
  path: String!
}

input UpdateUserInput {
  """user fullname"""
  fullname: String

  """user phone number"""
  phone: String

  """user role"""
  role: UserRoles

  """user password"""
  password: String

  """user active status"""
  userStatus: UserStatus
  id: String!
  faceInformation: FaceInformationInput
}

input FaceInformationInput {
  faceId: String
}

input IndexFaceInput {
  base64Img: String!
  userId: String!
}

input ClearFaceInput {
  userId: String!
}

input SignedUploadUrlInput {
  key: String!
  contentType: String!
  expiresIn: Float
}

input CreateShiftInput {
  locationId: ID!
  startDateTime: DateTime!
  endDateTime: DateTime!
  overTime: DateTime
  userIds: [ID!]!
  isRecurring: Boolean
  recurringId: String
}

input UpdateShiftInput {
  locationId: ID
  startDateTime: DateTime
  endDateTime: DateTime
  overTime: DateTime
  userIds: [ID!]
  isRecurring: Boolean
  recurringId: String
  fieldId: String!
}

input SignInInput {
  """user phone number"""
  phone: String!

  """user password"""
  password: String!
}

input CreateUserInput {
  """user fullname"""
  fullname: String!

  """user phone number"""
  phone: String!

  """user role"""
  role: UserRoles!

  """user password"""
  password: String!

  """user active status"""
  userStatus: UserStatus!
}

input CreateAllowanceInput {
  """Example field (placeholder)"""
  exampleField: Int!
}

input UpdateAllowanceInput {
  """Example field (placeholder)"""
  exampleField: Int
  id: Int!
}

input CreateAnouncementInput {
  users: [String!]
  userRoles: [UserRoles!]
  title: String!
  description: String!
  date: DateTime!
  document: String
}

input UpdateAnouncementInput {
  users: [String!]
  userRoles: [UserRoles!]
  title: String
  description: String
  date: DateTime
  document: String
}

input CreateUserDocumentInput {
  user: String!
  documentName: String!
  url: String!
}

input UpdateUserDocumentInput {
  user: String
  documentName: String
  url: String
}

input CreateHolidayInput {
  """name of the holiday"""
  name: String!

  """description of the holiday"""
  description: String!

  """date of the holiday"""
  date: DateTime!
}

input UpdateHolidayInput {
  """name of the holiday"""
  name: String

  """description of the holiday"""
  description: String

  """date of the holiday"""
  date: DateTime
}

input CreateInventoryInput {
  """Inventory item name"""
  item: String!
  description: String
  attributes: [AttributeTypeInput!]
  items: [InventoryItemInputType!]
  type: InventoryType!
}

input AttributeTypeInput {
  attibuteName: String!
  attributeValues: [String!]!
}

input InventoryItemInputType {
  item: String!
  quantity: Int!
  sku: String!
  costPrice: Float!
  sellingPrice: Float!
}

input CreateInventoryRequestInput {
  inventory: String!
  items: [RequestedItemInput!]!
  requestedBy: String!
}

input RequestedItemInput {
  item: String!
  sku: String!
  quantity: Int!
  selectedAttributes: [SelectedAttributeInput!]!
}

input SelectedAttributeInput {
  attributeName: String!
  value: String!
}

input UpdateInventoryRequestInput {
  status: RequestStatus!
  acceptedBy: String
}

input CreateLeaveInput {
  user: String!
  reason: String!
  leaveType: LeaveType!
  startDateTime: DateTime!
  endDateTime: DateTime!
}

input UpdateLeaveInput {
  approvedBy: String
  leaveStatus: LeaveStatus!
  rejectedReason: String
}

input CreatePaymentCorrectionInput {
  """Example field (placeholder)"""
  exampleField: Int!
}

input UpdatePaymentCorrectionInput {
  """Example field (placeholder)"""
  exampleField: Int
  id: Int!
}

input CreateTaskInput {
  """Example field (placeholder)"""
  exampleField: Int!
}

input UpdateTaskInput {
  """Example field (placeholder)"""
  exampleField: Int
  id: Int!
}

input CreateUserProfileInput {
  user: String!
  ic: String
  ID: String
  passport: String
  passportExpiresAt: DateTime
  permitNumber: String
  permitExpiresAt: DateTime
  gender: String
  dob: DateTime
  placeOfBirth: String
  currentAddress: String
  joinedAt: DateTime
  maritalStatus: String
  bankAccNumber: String
  bankName: String
  emergencyContact: [EmergencyContactInput!]
}

input EmergencyContactInput {
  name: String!
  relation: String!
  contact: ContactInput!
}

input ContactInput {
  countryCode: String!
  phone: String!
}

input UpdateUserProfileInput {
  user: String
  ic: String
  ID: String
  passport: String
  passportExpiresAt: DateTime
  permitNumber: String
  permitExpiresAt: DateTime
  gender: String
  dob: DateTime
  placeOfBirth: String
  currentAddress: String
  joinedAt: DateTime
  maritalStatus: String
  bankAccNumber: String
  bankName: String
  emergencyContact: [EmergencyContactInput!]
}

input ClockInInput {
  shiftId: String!
  locationId: String!
  date: DateTime!
  base64Img: String!
}

input ClockOutInput {
  attendanceId: String!
  base64Img: String!
}

input CreateAttendanceInput {
  shiftId: String!
  userId: String!
  locationId: String!
  date: DateTime!
  startTime: DateTime!
  endTime: DateTime!
  overTime: DateTime!
}

input UpdateAttendanceInput {
  shiftId: String!
  userId: String!
  locationId: String!
  date: DateTime!
  startTime: DateTime!
  endTime: DateTime!
  overTime: DateTime!
  id: String!
}

input CreatePaymentConfigInput {
  """Example field (placeholder)"""
  exampleField: Int!
}

input UpdatePaymentConfigInput {
  """Example field (placeholder)"""
  exampleField: Int
  id: Int!
}

input ClaimInput {
  user: String!
  amount: Float
  purpose: String
  claimType: ClaimType!
  from: DateTime
  to: DateTime
  workingHours: Int
  receipts: [String!]
  items: [String!]
  date: DateTime
  client: String
  toll: String
  distance: Int
  site: String
}

input UpdateClaimInput {
  user: String
  amount: Float
  purpose: String
  claimType: ClaimType!
  from: DateTime
  to: DateTime
  workingHours: Int
  receipts: [String!]
  items: [String!]
  date: DateTime
  client: String
  toll: String
  distance: Int
  site: String

  """Updated claim status"""
  status: ClaimStatus

  """User ID of the person who processed the claim"""
  processedBy: String

  """Reason for rejection, if applicable"""
  rejectedReason: String
}

input CreateCheckpointInput {
  name: String!
  location: String!
  locationCoordinates: [Float!]
}

input UpdateCheckpointInput {
  name: String!
  locationCoordinates: [Float!]
}

input CreateCheckpointAttendanceInput {
  checkpointId: String!
  locationId: String!
  scannedLocation: [Float!]
}

input CreateIncidentInput {
  location: String!
  description: String!
  priorityLevel: PriorityLevel!
  evidence: [EvidenceInput!]!
}

input EvidenceInput {
  type: EvidenceType!
  url: String!
}

input UpdateIncidentInput {
  location: String
  description: String
  priorityLevel: PriorityLevel
  evidence: [EvidenceInput!]
}