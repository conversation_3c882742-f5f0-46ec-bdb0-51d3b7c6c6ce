import { ConflictException, Injectable } from '@nestjs/common';
import { CreateUserInput } from './dto/create-user.input';
import { UpdateUserInput } from './dto/update-user.input';
import { InjectModel } from '@nestjs/mongoose';
import { User } from './entities/user.entity';
import { FilterQuery, Model } from 'mongoose';
import * as argon from 'argon2';
import { AwsService } from 'src/aws/aws.service';
import { IndexFaceInput } from './dto/index-face.input';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private user: Model<User>,
    private readonly awsService: AwsService,
  ) {}

  async create(createUserInput: CreateUserInput) {
    const userExists = await this.user.exists({
      phone: createUserInput.phone,
    });
    if (userExists) throw new ConflictException('User already exists');

    return this.user.create(createUserInput);
  }

  findAll(filter: FilterQuery<User> = {}) {
    return this.user.find(filter);
  }

  findOne(filter: FilterQuery<User>) {
    return this.user.findOne(filter);
  }

  async update(id: string, updateUserInput: UpdateUserInput) {
    if (updateUserInput.password)
      updateUserInput.password = await argon.hash(updateUserInput.password);
    return this.user.findByIdAndUpdate(id, updateUserInput, { new: true });
  }

  searchFace(base64Img: string) {
    const base64Data = base64Img.replace(/^data:image\/\w+;base64,/, '');
    return this.awsService.searchFace(Buffer.from(base64Data, 'base64'));
  }

  async indexFace(input: IndexFaceInput) {
    const base64Data = input.base64Img.replace(/^data:image\/\w+;base64,/, '');
    return this.awsService.indexFace(
      Buffer.from(base64Data, 'base64'),
      input.userId,
    );
  }

  deleteFaceIndex(faceId: string) {
    return this.awsService.deleteFace(faceId);
  }
}
