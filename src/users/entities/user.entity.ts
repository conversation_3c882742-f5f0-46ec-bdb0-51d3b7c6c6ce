import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';

export enum UserRoles {
  ADMIN = 'ADMIN',
  OPERATIONS_ADMIN = 'OPERATIONS_ADMIN',
  OPERATIONS_MANAGER = 'OPERATIONS_MANAGER',
  HR_ADMIN = 'HR_ADMIN',
  LOCAL_GUARD = 'LOCAL_GUARD',
  NEPAL_GUARD = 'NEPAL_GUARD',
  BUFFER_GUARD = 'BUFFER_GUARD',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

registerEnumType(UserRoles, { name: 'UserRoles' });
registerEnumType(UserStatus, { name: 'UserStatus' });

@ObjectType('FaceInformation')
export class FaceInformation {
  @Field(() => String, { nullable: true })
  faceId?: string;
}

@ObjectType()
@Schema()
export class User extends MongooseSchema {
  @Field(() => String, { description: 'user fullname' })
  @Prop({ required: true })
  fullname: string;

  @Field(() => String, { description: 'user phone number' })
  @Prop({ required: true, unique: true })
  phone: string;

  @Field(() => UserStatus, { description: 'user active status' })
  @Prop({ required: true, default: UserStatus.ACTIVE, enum: UserStatus })
  userStatus: UserStatus;

  @Field(() => UserRoles, { description: 'user role' })
  @Prop({ required: true, enum: Object.values(UserRoles) })
  role: UserRoles;

  @Prop({ required: true })
  password: string;

  @Field(() => FaceInformation, { nullable: true })
  @Prop({ type: { faceId: String } })
  faceInformation?: FaceInformation;
}

export const UserSchema = SchemaFactory.createForClass(User);
